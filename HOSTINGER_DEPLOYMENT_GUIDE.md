# Hostinger Deployment Guide for Laravel E-commerce Application

## Prerequisites

- <PERSON><PERSON> hosting account with PHP support
- Domain name configured
- FTP/SFTP access credentials
- Database access credentials
- Local development environment with the application ready

## Step 1: Prepare Your Local Application

### 1.1 Optimize for Production
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Install production dependencies
composer install --optimize-autoloader --no-dev
```

### 1.2 Update Environment Configuration
Create a production `.env` file:
```env
APP_NAME="TS Essential"
APP_ENV=production
APP_KEY=base64:YOUR_APP_KEY_HERE
APP_DEBUG=false
APP_URL="https://yourdomain.com"
APP_TIMEZONE="UTC"
SYSTEM_KEY="123456"

DEMO_MODE="Off"

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST="localhost"
DB_PORT="3306"
DB_DATABASE="your_hostinger_database_name"
DB_USERNAME="your_hostinger_db_username"
DB_PASSWORD="your_hostinger_db_password"

BROADCAST_DRIVER=log
CACHE_DRIVER="file"
QUEUE_CONNECTION=sync
SESSION_DRIVER="file"
SESSION_LIFETIME=120

MAIL_DRIVER="smtp"
MAIL_HOST="smtp.hostinger.com"
MAIL_PORT="465"
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="your_email_password"
MAIL_ENCRYPTION="ssl"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="TS Essential"
```

## Step 2: Database Setup on Hostinger

### 2.1 Create Database
1. Login to Hostinger hPanel
2. Go to **Databases** → **MySQL Databases**
3. Create a new database (e.g., `u123456789_tsessential`)
4. Create a database user with full privileges
5. Note down the database credentials

### 2.2 Import Database
1. Export your local database:
```bash
mysqldump -u root -p your_local_database > database_backup.sql
```

2. Import to Hostinger via phpMyAdmin:
   - Access phpMyAdmin from hPanel
   - Select your database
   - Go to **Import** tab
   - Upload your `database_backup.sql` file
   - Click **Go**

## Step 3: File Upload to Hostinger

### 3.1 Prepare Files for Upload
Create a deployment package excluding unnecessary files:
```bash
# Create a clean copy
cp -r /path/to/your/project /path/to/deployment/package

# Remove development files
rm -rf node_modules/
rm -rf .git/
rm -rf tests/
rm -rf storage/logs/*
rm .env
```

### 3.2 Upload Files
**Option A: Using File Manager (Recommended for beginners)**
1. Login to Hostinger hPanel
2. Go to **File Manager**
3. Navigate to `public_html` folder
4. Upload your project files
5. Extract if uploaded as ZIP

**Option B: Using FTP/SFTP**
```bash
# Using SFTP
sftp your_username@your_domain.com
put -r /path/to/deployment/package/* /public_html/
```

### 3.3 Set Correct File Structure
Your Hostinger file structure should look like:
```
public_html/
├── app/
├── bootstrap/
├── config/
├── database/
├── resources/
├── routes/
├── storage/
├── vendor/
├── .env
├── artisan
├── composer.json
├── index.php (Laravel's public/index.php moved here)
└── ... (other Laravel files)
```

## Step 4: Configure Hostinger for Laravel

### 4.1 Move Public Files
1. Copy contents of `public/` folder to `public_html/`
2. Update `index.php` to point to correct paths:
```php
// In public_html/index.php
require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
```

### 4.2 Set Directory Permissions
Using File Manager, set permissions:
- `storage/` and all subdirectories: 755
- `bootstrap/cache/`: 755
- `.env` file: 644

### 4.3 Create .htaccess File
Create `.htaccess` in `public_html/`:
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle Angular and Vue.js routes
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ index.php [QSA,L]
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # Hide sensitive files
    <Files .env>
        Order allow,deny
        Deny from all
    </Files>
</IfModule>

# Disable directory browsing
Options -Indexes

# Set default charset
AddDefaultCharset UTF-8
```

## Step 5: Environment Configuration

### 5.1 Create Production .env File
Using File Manager, create `.env` file in root directory with production settings (see Step 1.2)

### 5.2 Generate Application Key
If you need to generate a new key:
1. Use online Laravel key generator, or
2. Run locally: `php artisan key:generate --show`
3. Copy the key to your `.env` file

## Step 6: Final Configuration

### 6.1 Clear and Cache Configuration
Access your site via SSH (if available) or use a web-based terminal:
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 6.2 Set Up Storage Link
```bash
php artisan storage:link
```

### 6.3 Run Database Migrations (if needed)
```bash
php artisan migrate --force
```

## Step 7: SSL Certificate Setup

### 7.1 Enable SSL in Hostinger
1. Go to hPanel → **SSL**
2. Enable **Free SSL Certificate**
3. Wait for activation (usually 10-15 minutes)

### 7.2 Force HTTPS
Add to your `.htaccess`:
```apache
# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## Step 8: Testing and Troubleshooting

### 8.1 Test Your Application
1. Visit your domain: `https://yourdomain.com`
2. Test key functionality:
   - Homepage loading
   - Product pages
   - User registration/login
   - Admin panel access
   - Email functionality

### 8.2 Common Issues and Solutions

**Issue: 500 Internal Server Error**
- Check file permissions (storage/ should be 755)
- Verify .env file configuration
- Check error logs in hPanel

**Issue: Database Connection Error**
- Verify database credentials in .env
- Ensure database user has proper privileges
- Check if database exists

**Issue: Missing CSS/JS Files**
- Ensure all public assets are in public_html/
- Check file paths in templates
- Verify .htaccess configuration

**Issue: Email Not Working**
- Verify SMTP settings in .env
- Test with Hostinger's SMTP settings
- Check spam folders

## Step 9: Post-Deployment Tasks

### 9.1 Set Up Cron Jobs (if needed)
1. Go to hPanel → **Cron Jobs**
2. Add Laravel scheduler:
```bash
* * * * * cd /home/<USER>/public_html && php artisan schedule:run >> /dev/null 2>&1
```

### 9.2 Configure Backups
1. Set up automatic database backups in hPanel
2. Consider file backup solutions
3. Test restore procedures

### 9.3 Monitor Performance
- Enable error logging
- Monitor site speed
- Set up uptime monitoring

## Step 10: Security Hardening

### 10.1 Additional Security Measures
1. Change default admin credentials
2. Update all passwords
3. Enable two-factor authentication
4. Regular security updates

### 10.2 Hide Laravel Version
Add to `.htaccess`:
```apache
# Hide Laravel
<Files composer.json>
    Order allow,deny
    Deny from all
</Files>
```

## Maintenance Checklist

- [ ] Regular backups scheduled
- [ ] SSL certificate auto-renewal enabled
- [ ] Error monitoring set up
- [ ] Performance monitoring configured
- [ ] Security updates planned
- [ ] Database optimization scheduled

## Support Resources

- **Hostinger Knowledge Base**: https://support.hostinger.com
- **Laravel Documentation**: https://laravel.com/docs
- **Emergency Contacts**: Keep Hostinger support details handy

## Advanced Configuration

### A1: Custom PHP Configuration
Create `php.ini` in your root directory:
```ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 64M
post_max_size = 64M
max_input_vars = 3000
```

### A2: Queue Configuration (Optional)
If using queues, set up supervisor or cron-based queue worker:
```bash
# Add to cron jobs
* * * * * cd /home/<USER>/public_html && php artisan queue:work --stop-when-empty
```

### A3: Redis Configuration (Premium Plans)
If your Hostinger plan supports Redis:
```env
REDIS_HOST="127.0.0.1"
REDIS_PASSWORD="your_redis_password"
REDIS_PORT="6379"
CACHE_DRIVER="redis"
SESSION_DRIVER="redis"
```

## Specific E-commerce Configuration

### E1: Payment Gateway Setup
Update your `.env` with production payment credentials:
```env
# PayPal
PAYPAL_CLIENT_ID="your_production_paypal_client_id"
PAYPAL_CLIENT_SECRET="your_production_paypal_secret"
PAYPAL_MODE="live"

# Stripe
STRIPE_KEY="pk_live_your_stripe_key"
STRIPE_SECRET="sk_live_your_stripe_secret"

# Other payment gateways as needed
```

### E2: File Upload Configuration
Ensure proper file upload settings:
```php
// In config/filesystems.php - verify public disk configuration
'public' => [
    'driver' => 'local',
    'root' => storage_path('app/public'),
    'url' => env('APP_URL').'/storage',
    'visibility' => 'public',
],
```

### E3: Image Optimization
Add image optimization settings:
```env
# Image settings
IMAGE_DRIVER="gd"
IMAGE_QUALITY="80"
```

## Performance Optimization

### P1: Enable OPcache
Add to your `php.ini`:
```ini
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

### P2: Database Optimization
```sql
-- Run these queries in phpMyAdmin for better performance
OPTIMIZE TABLE products;
OPTIMIZE TABLE orders;
OPTIMIZE TABLE users;
-- Add indexes for frequently queried columns
```

### P3: Asset Optimization
```bash
# Before deployment, optimize assets
npm run production
php artisan optimize
```

## Monitoring and Logging

### M1: Error Logging Setup
Create custom error logging:
```php
// In config/logging.php
'channels' => [
    'production' => [
        'driver' => 'daily',
        'path' => storage_path('logs/laravel.log'),
        'level' => 'error',
        'days' => 14,
    ],
],
```

### M2: Application Monitoring
Set up basic monitoring:
```bash
# Create a health check endpoint
# Add to routes/web.php
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'database' => DB::connection()->getPdo() ? 'connected' : 'disconnected'
    ]);
});
```

## Backup Strategy

### B1: Automated Database Backup Script
Create `backup.php` in your root:
```php
<?php
$host = 'localhost';
$username = 'your_db_username';
$password = 'your_db_password';
$database = 'your_database_name';
$backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';

$command = "mysqldump --host=$host --user=$username --password=$password $database > $backup_file";
exec($command);

// Optional: Upload to cloud storage or email
?>
```

### B2: File Backup
```bash
# Create weekly file backup (add to cron)
0 2 * * 0 tar -czf /home/<USER>/backups/files_$(date +\%Y\%m\%d).tar.gz /home/<USER>/public_html --exclude='storage/logs' --exclude='node_modules'
```

## Troubleshooting Guide

### T1: Debug Mode for Troubleshooting
Temporarily enable debug mode:
```env
APP_DEBUG=true
LOG_LEVEL=debug
```
**Remember to disable after fixing issues!**

### T2: Common Error Solutions

**Laravel Mix Errors:**
```bash
# If assets are missing
npm install
npm run production
```

**Permission Errors:**
```bash
# Fix storage permissions
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

**Database Migration Issues:**
```bash
# Reset migrations (CAUTION: This will delete data)
php artisan migrate:fresh --seed
```

### T3: Performance Issues
```bash
# Clear all caches
php artisan optimize:clear

# Rebuild caches
php artisan optimize
```

## Security Checklist

- [ ] Change all default passwords
- [ ] Enable SSL certificate
- [ ] Hide sensitive files (.env, composer.json)
- [ ] Set proper file permissions
- [ ] Enable security headers
- [ ] Regular security updates
- [ ] Monitor access logs
- [ ] Set up fail2ban (if available)
- [ ] Use strong database passwords
- [ ] Enable two-factor authentication

## Go-Live Checklist

- [ ] Domain DNS configured
- [ ] SSL certificate active
- [ ] Database imported and configured
- [ ] All files uploaded
- [ ] Environment variables set
- [ ] Caches cleared and rebuilt
- [ ] Storage link created
- [ ] Email configuration tested
- [ ] Payment gateways tested
- [ ] Admin panel accessible
- [ ] User registration working
- [ ] Product pages loading
- [ ] Search functionality working
- [ ] Backup system configured
- [ ] Monitoring set up

---

**Important Notes:**
- Always test on a staging environment first
- Keep backups before making changes
- Monitor error logs after deployment
- Replace all placeholder values with actual production values
- Consider using a staging subdomain for testing

**Emergency Rollback Plan:**
1. Keep previous version backup
2. Document all changes made
3. Have database rollback script ready
4. Know how to quickly restore from backup
