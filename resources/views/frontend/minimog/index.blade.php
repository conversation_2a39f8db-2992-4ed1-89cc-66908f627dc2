@extends('frontend.minimog.layouts.app')

@section('content')
<style>
    /* Minimog Custom Styles */
    :root {
        --minimog-black: #000000;
        --minimog-white: #FFFFFF;
        --minimog-gray: #666666;
        --minimog-light-gray: #999999;
        --minimog-accent: #FF4D4D;
        --minimog-bg-light: #F9F9F9;
        --minimog-border: #EAEAEA;
        --minimog-green: #2ECC71;
        --minimog-hover: #333333;
    }

    .minimog-hero {
        background: var(--minimog-white);
        min-height: 70vh;
        position: relative;
        overflow: hidden;
    }

    .minimog-hero-content {
        position: absolute;
        left: 50px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
    }

    .minimog-hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        color: var(--minimog-black);
        line-height: 1.2;
        margin-bottom: 2rem;
    }

    .minimog-btn {
        background: var(--minimog-black);
        color: var(--minimog-white);
        padding: 15px 40px;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .minimog-btn:hover {
        background: var(--minimog-hover);
        color: var(--minimog-white);
        text-decoration: none;
    }

    .minimog-product-card {
        background: var(--minimog-white);
        border: 1px solid var(--minimog-border);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .minimog-product-card:hover {
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transform: translateY(-5px);
    }

    .minimog-product-image {
        position: relative;
        overflow: hidden;
        aspect-ratio: 1;
    }

    .minimog-product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .minimog-product-card:hover .minimog-product-image img {
        transform: scale(1.05);
    }

    .minimog-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background: var(--minimog-green);
        color: var(--minimog-white);
        padding: 4px 8px;
        font-size: 12px;
        font-weight: 600;
        border-radius: 3px;
    }

    .minimog-badge.sale {
        background: var(--minimog-accent);
    }

    .minimog-product-info {
        padding: 20px;
    }

    .minimog-product-title {
        color: var(--minimog-black);
        font-weight: 500;
        margin-bottom: 10px;
        font-size: 14px;
        line-height: 1.4;
    }

    .minimog-product-price {
        font-weight: 600;
        color: var(--minimog-black);
    }

    .minimog-product-price .old-price {
        color: var(--minimog-light-gray);
        text-decoration: line-through;
        margin-right: 8px;
    }

    .minimog-product-price .sale-price {
        color: var(--minimog-accent);
    }

    .minimog-color-swatches {
        display: flex;
        gap: 5px;
        margin-top: 10px;
    }

    .minimog-color-swatch {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .minimog-color-swatch:hover {
        border-color: var(--minimog-black);
    }

    .minimog-category-card {
        background: var(--minimog-bg-light);
        padding: 40px 20px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .minimog-category-card:hover {
        transform: scale(1.02);
    }

    .minimog-category-card img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        margin-bottom: 20px;
        transition: transform 0.3s ease;
    }

    .minimog-category-card:hover img {
        transform: scale(1.1);
    }

    .minimog-category-title {
        color: var(--minimog-black);
        font-weight: 600;
        margin-bottom: 5px;
    }

    .minimog-category-count {
        color: var(--minimog-gray);
        font-size: 14px;
    }

    .minimog-section-title {
        text-align: center;
        margin-bottom: 50px;
    }

    .minimog-section-title h2 {
        color: var(--minimog-black);
        font-weight: 700;
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .minimog-section-title p {
        color: var(--minimog-gray);
        font-size: 16px;
    }

    .minimog-promo-bar {
        background: var(--minimog-accent);
        color: var(--minimog-white);
        text-align: center;
        padding: 10px 0;
        font-weight: 600;
        position: relative;
    }

    .minimog-promo-close {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--minimog-white);
        font-size: 18px;
        cursor: pointer;
    }

    @media (max-width: 768px) {
        .minimog-hero-title {
            font-size: 2.5rem;
        }
        
        .minimog-hero-content {
            left: 20px;
            right: 20px;
        }
    }
</style>

<!-- Promo Bar -->
<div class="minimog-promo-bar">
    <div class="container">
        Free shipping for all orders from $60+
        <button class="minimog-promo-close" onclick="this.parentElement.parentElement.style.display='none'">&times;</button>
    </div>
</div>

<!-- Hero Section -->
<section class="minimog-hero">
    <div class="container-fluid p-0">
        <div class="row no-gutters">
            <div class="col-12 position-relative">
                @php
                    $hero_image = get_setting('home_slider_images') ? json_decode(get_setting('home_slider_images'))[0] ?? null : null;
                @endphp
                @if($hero_image)
                    <img src="{{ uploaded_asset($hero_image) }}" alt="Hero Image" style="width: 100%; height: 70vh; object-fit: cover;">
                @else
                    <img src="{{ static_asset('assets/img/placeholder-rect.jpg') }}" alt="Hero Image" style="width: 100%; height: 70vh; object-fit: cover;">
                @endif
                
                <div class="minimog-hero-content">
                    <p style="color: var(--minimog-gray); margin-bottom: 10px; font-weight: 500;">New Arrivals</p>
                    <h1 class="minimog-hero-title">Ultimate Winter<br>Warmer</h1>
                    <a href="{{ route('products') }}" class="minimog-btn">Shop Now</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Best Sellers Section -->
<section class="py-5">
    <div class="container">
        <div class="minimog-section-title">
            <p style="color: var(--minimog-gray); margin-bottom: 10px;">You are in</p>
            <h2>Best Sellers</h2>
        </div>
        
        <div class="row" id="section_best_selling">
            @include('frontend.minimog.partials.best_selling_section')
        </div>
    </div>
</section>

<!-- Shop by Categories Section -->
<section class="py-5" style="background: var(--minimog-white);">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 style="color: var(--minimog-black); font-weight: 700; margin: 0;">Shop by Categories</h2>
            <div class="d-flex align-items-center">
                <span style="color: var(--minimog-gray);">1/5</span>
                <button class="btn btn-link text-dark ml-2">&lt;</button>
                <button class="btn btn-link text-dark">&gt;</button>
            </div>
        </div>
        
        <div class="row">
            @foreach($featured_categories->take(4) as $category)
            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ route('products.category', $category->slug) }}" class="text-decoration-none">
                    <div class="minimog-category-card">
                        @if($category->banner)
                            <img src="{{ uploaded_asset($category->banner) }}" alt="{{ $category->getTranslation('name') }}">
                        @else
                            <img src="{{ static_asset('assets/img/placeholder.jpg') }}" alt="{{ $category->getTranslation('name') }}">
                        @endif
                        <h4 class="minimog-category-title">{{ $category->getTranslation('name') }}</h4>
                        <p class="minimog-category-count">{{ $category->products->count() }} items</p>
                    </div>
                </a>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="py-5" id="section_featured">
    @include('frontend.minimog.partials.featured_section')
</section>

@endsection

@section('script')
<script>
    $(document).ready(function() {
        // Load sections via AJAX for better performance
        if (typeof loadBestSellingSection === 'function') {
            loadBestSellingSection();
        }
        
        if (typeof loadFeaturedSection === 'function') {
            loadFeaturedSection();
        }
    });
</script>
@endsection
