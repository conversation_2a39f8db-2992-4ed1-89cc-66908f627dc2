<style>
    .minimog-nav {
        background: var(--minimog-white);
        border-bottom: 1px solid var(--minimog-border);
        position: sticky;
        top: 0;
        z-index: 1000;
    }

    .minimog-nav .navbar-brand {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--minimog-black) !important;
        text-decoration: none;
        letter-spacing: -1px;
    }

    .minimog-nav .navbar-nav .nav-link {
        color: var(--minimog-black) !important;
        font-weight: 500;
        padding: 1rem 1.5rem !important;
        transition: color 0.3s ease;
    }

    .minimog-nav .navbar-nav .nav-link:hover {
        color: var(--minimog-accent) !important;
    }

    .minimog-nav .nav-icons {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .minimog-nav .nav-icon {
        color: var(--minimog-black);
        font-size: 1.2rem;
        transition: color 0.3s ease;
        text-decoration: none;
        position: relative;
    }

    .minimog-nav .nav-icon:hover {
        color: var(--minimog-accent);
        text-decoration: none;
    }

    .minimog-nav .cart-count {
        position: absolute;
        top: -8px;
        right: -8px;
        background: var(--minimog-accent);
        color: var(--minimog-white);
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-size: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }

    .minimog-search-box {
        position: relative;
        max-width: 400px;
        margin: 0 2rem;
    }

    .minimog-search-input {
        border: 1px solid var(--minimog-border);
        padding: 0.75rem 1rem;
        width: 100%;
        border-radius: 0;
        font-size: 14px;
    }

    .minimog-search-input:focus {
        outline: none;
        border-color: var(--minimog-black);
    }

    .minimog-search-btn {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--minimog-gray);
    }

    @media (max-width: 991px) {
        .minimog-search-box {
            margin: 1rem 0;
            max-width: 100%;
        }
        
        .minimog-nav .nav-icons {
            justify-content: center;
            margin-top: 1rem;
        }
    }
</style>

<nav class="navbar navbar-expand-lg minimog-nav">
    <div class="container">
        <!-- Brand -->
        <a class="navbar-brand" href="{{ route('search') }}">
            <img src="{{ static_asset('assets/img/ts-essential-logo.png') }}" alt="ts essential" style="height: 40px;">
        </a>

        <!-- Mobile toggle -->
        <button class="navbar-toggler border-0" type="button" data-toggle="collapse" data-target="#minimogNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation -->
        <div class="collapse navbar-collapse" id="minimogNav">
            <!-- Main Menu -->
            <ul class="navbar-nav mx-auto">
                <li class="nav-item">
                    <a class="nav-link" href="{{ route('home') }}">Home</a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{{ route('categories.all') }}">Categories</a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{{ route('flash-deals') }}">Flash Sale</a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{{ route('blog') }}">Blogs</a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{{ route('brands.all') }}">All Brands</a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{{ route('categories.all') }}">All Categories</a>
                </li>
            </ul>

            <!-- Search Box -->
            <div class="minimog-search-box d-none d-lg-block">
                <form action="{{ route('search') }}" method="GET">
                    <input type="text" name="keyword" class="minimog-search-input" placeholder="Search products..." 
                           @isset($query) value="{{ $query }}" @endisset>
                    <button type="submit" class="minimog-search-btn">
                        <i class="las la-search"></i>
                    </button>
                </form>
            </div>

            <!-- Right Icons -->
            <div class="minimog-nav-icons d-flex align-items-center">
                <!-- Search Icon (Mobile) -->
                <a href="#" class="nav-icon d-lg-none" data-toggle="modal" data-target="#searchModal">
                    <i class="las la-search"></i>
                </a>

                <!-- User Account -->
                @auth
                    <div class="dropdown">
                        <a href="#" class="nav-icon dropdown-toggle" data-toggle="dropdown">
                            <i class="las la-user"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item" href="{{ route('dashboard') }}">Dashboard</a>
                            <a class="dropdown-item" href="{{ route('purchase_history.index') }}">Purchase History</a>
                            <a class="dropdown-item" href="{{ route('wishlists.index') }}">Wishlist</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ route('logout') }}"
                               onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                Logout
                            </a>
                            <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                @csrf
                            </form>
                        </div>
                    </div>
                @else
                    <a href="{{ route('user.login') }}" class="nav-icon">
                        <i class="las la-user"></i>
                    </a>
                @endauth

                <!-- Wishlist -->
                @auth
                    <a href="{{ route('wishlists.index') }}" class="nav-icon">
                        <i class="las la-heart"></i>
                        @if(count(\App\Models\Wishlist::where('user_id', auth()->id())->get()) > 0)
                            <span class="cart-count">{{ count(\App\Models\Wishlist::where('user_id', auth()->id())->get()) }}</span>
                        @endif
                    </a>
                @endauth

                <!-- Shopping Cart -->
                <a href="{{ route('cart') }}" class="nav-icon">
                    <i class="las la-shopping-bag"></i>
                    @if(Session::has('cart') && count(Session::get('cart')) > 0)
                        <span class="cart-count">{{ count(Session::get('cart')) }}</span>
                    @endif
                </a>
            </div>
        </div>
    </div>
</nav>

<!-- Mobile Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <form action="{{ route('search') }}" method="GET">
                    <div class="input-group">
                        <input type="text" name="keyword" class="form-control" placeholder="Search products..." 
                               @isset($query) value="{{ $query }}" @endisset>
                        <div class="input-group-append">
                            <button type="submit" class="btn btn-primary">Search</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
