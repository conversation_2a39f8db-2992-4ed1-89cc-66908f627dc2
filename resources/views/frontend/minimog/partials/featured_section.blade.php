@php
    $featured_products = get_featured_products(8);
@endphp

<div class="container">
    <div class="minimog-section-title">
        <h2>Featured Products</h2>
        <p>Discover our handpicked selection of premium items</p>
    </div>
    
    <div class="row">
        @if(count($featured_products) > 0)
            @foreach($featured_products as $key => $product)
                <div class="col-lg-3 col-md-6 col-sm-6 mb-4">
                    <div class="minimog-product-card">
                        <div class="minimog-product-image">
                            <a href="{{ route('product', $product->slug) }}">
                                @if($product->thumbnail_img)
                                    <img src="{{ uploaded_asset($product->thumbnail_img) }}" alt="{{ $product->getTranslation('name') }}">
                                @else
                                    <img src="{{ static_asset('assets/img/placeholder.jpg') }}" alt="{{ $product->getTranslation('name') }}">
                                @endif
                            </a>
                            
                            @if($product->created_at >= Carbon\Carbon::now()->subDays(30))
                                <span class="minimog-badge">New</span>
                            @elseif($product->discount > 0)
                                <span class="minimog-badge sale">Sale</span>
                            @endif
                        </div>
                        
                        <div class="minimog-product-info">
                            <h5 class="minimog-product-title">
                                <a href="{{ route('product', $product->slug) }}" class="text-decoration-none" style="color: var(--minimog-black);">
                                    {{ Str::limit($product->getTranslation('name'), 50) }}
                                </a>
                            </h5>
                            
                            <div class="minimog-product-price">
                                @if(home_discounted_base_price($product) != home_base_price($product))
                                    <span class="old-price">${{ home_base_price($product) }}</span>
                                    <span class="sale-price">${{ home_discounted_base_price($product) }}</span>
                                @else
                                    <span>${{ home_base_price($product) }}</span>
                                @endif
                            </div>
                            
                            @if($product->colors && count(json_decode($product->colors)) > 0)
                                <div class="minimog-color-swatches">
                                    @foreach(array_slice(json_decode($product->colors), 0, 4) as $color)
                                        @php
                                            $color_name = \App\Models\Color::where('code', $color)->first();
                                        @endphp
                                        @if($color_name)
                                            <div class="minimog-color-swatch" style="background-color: {{ $color }};" title="{{ $color_name->name }}"></div>
                                        @endif
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <!-- Fallback demo products if no featured products -->
            @php
                $demo_products = \App\Models\Product::skip(8)->take(8)->get();
            @endphp
            @foreach($demo_products as $key => $product)
                <div class="col-lg-3 col-md-6 col-sm-6 mb-4">
                    <div class="minimog-product-card">
                        <div class="minimog-product-image">
                            <a href="{{ route('product', $product->slug) }}">
                                @if($product->thumbnail_img)
                                    <img src="{{ uploaded_asset($product->thumbnail_img) }}" alt="{{ $product->getTranslation('name') }}">
                                @else
                                    <img src="{{ static_asset('assets/img/placeholder.jpg') }}" alt="{{ $product->getTranslation('name') }}">
                                @endif
                            </a>
                            
                            @if($key < 2)
                                <span class="minimog-badge">New</span>
                            @elseif($key < 4)
                                <span class="minimog-badge sale">Sale</span>
                            @endif
                        </div>
                        
                        <div class="minimog-product-info">
                            <h5 class="minimog-product-title">
                                <a href="{{ route('product', $product->slug) }}" class="text-decoration-none" style="color: var(--minimog-black);">
                                    {{ Str::limit($product->getTranslation('name'), 50) }}
                                </a>
                            </h5>
                            
                            <div class="minimog-product-price">
                                @if(home_discounted_base_price($product) != home_base_price($product))
                                    <span class="old-price">${{ home_base_price($product) }}</span>
                                    <span class="sale-price">${{ home_discounted_base_price($product) }}</span>
                                @else
                                    <span>${{ home_base_price($product) }}</span>
                                @endif
                            </div>
                            
                            <!-- Demo color swatches -->
                            <div class="minimog-color-swatches">
                                <div class="minimog-color-swatch" style="background-color: #000000;" title="Black"></div>
                                <div class="minimog-color-swatch" style="background-color: #FFFFFF; border: 1px solid #ddd;" title="White"></div>
                                <div class="minimog-color-swatch" style="background-color: #F5F5DC;" title="Beige"></div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @endif
    </div>
    
    <div class="text-center mt-4">
        <a href="{{ route('products') }}" class="minimog-btn">View All Products</a>
    </div>
</div>
