<!doctype html>
@if (\App\Models\Language::where('code', Session::get('locale', Config::get('app.locale')))->first()->rtl == 1)
    <html dir="rtl" lang="{{ str_replace('_', '-', app()->getLocale()) }}">
@else
    <html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
@endif

<head>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="app-url" content="{{ getBaseURL() }}">
    <meta name="file-base-url" content="{{ getFileBaseURL() }}">

    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Favicon -->
    @php
        $favicon = get_setting('site_icon');
    @endphp
    @if ($favicon != null)
        <link rel="icon" href="{{ uploaded_asset($favicon) }}">
    @else
        <link rel="icon" href="{{ static_asset('assets/img/favicon.ico') }}">
    @endif

    <title>
        @yield('meta_title', get_setting('meta_title'))
    </title>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="@yield('meta_description', get_setting('meta_description'))">
    <meta name="keywords" content="@yield('meta_keywords', get_setting('meta_keywords'))">

    @yield('meta')

    @if (!isset($detailedProduct) && !isset($shop) && !isset($page) && !isset($blog))
        <!-- Schema.org markup for Google+ -->
        <meta itemprop="name" content="{{ get_setting('meta_title') }}">
        <meta itemprop="description" content="{{ get_setting('meta_description') }}">
        <meta itemprop="image" content="{{ uploaded_asset(get_setting('meta_image')) }}">

        <!-- Twitter Card data -->
        <meta name="twitter:card" content="product">
        <meta name="twitter:site" content="@publisher_handle">
        <meta name="twitter:title" content="{{ get_setting('meta_title') }}">
        <meta name="twitter:description" content="{{ get_setting('meta_description') }}">
        <meta name="twitter:creator" content="@author_handle">
        <meta name="twitter:image" content="{{ uploaded_asset(get_setting('meta_image')) }}">

        <!-- Open Graph data -->
        <meta property="og:title" content="{{ get_setting('meta_title') }}" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="{{ route('home') }}" />
        <meta property="og:image" content="{{ uploaded_asset(get_setting('meta_image')) }}" />
        <meta property="og:description" content="{{ get_setting('meta_description') }}" />
        <meta property="og:site_name" content="{{ env('APP_NAME') }}" />
    @endif

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- CSS Files -->
    <link rel="stylesheet" href="{{ static_asset('assets/css/vendors.css') }}">
    @if (\App\Models\Language::where('code', Session::get('locale', Config::get('app.locale')))->first()->rtl == 1)
        <link rel="stylesheet" href="{{ static_asset('assets/css/bootstrap-rtl.min.css') }}">
    @endif
    <link rel="stylesheet" href="{{ static_asset('assets/css/aiz-core.css?v=') }}{{ rand(1000, 9999) }}">
    <link rel="stylesheet" href="{{ static_asset('assets/css/custom-style.css') }}">

    <style>
        :root {
            --minimog-black: #000000;
            --minimog-white: #FFFFFF;
            --minimog-gray: #666666;
            --minimog-light-gray: #999999;
            --minimog-accent: #FF4D4D;
            --minimog-bg-light: #F9F9F9;
            --minimog-border: #EAEAEA;
            --minimog-green: #2ECC71;
            --minimog-hover: #333333;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: var(--minimog-black);
            background: var(--minimog-white);
        }

        .minimog-main-wrapper {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .minimog-content {
            flex: 1;
        }

        /* Override default styles */
        .btn-primary {
            background: var(--minimog-black);
            border-color: var(--minimog-black);
        }

        .btn-primary:hover {
            background: var(--minimog-hover);
            border-color: var(--minimog-hover);
        }

        .text-primary {
            color: var(--minimog-black) !important;
        }

        .bg-primary {
            background: var(--minimog-black) !important;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--minimog-bg-light);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--minimog-gray);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--minimog-black);
        }

        /* Navigation Dropdown Hover Effects */
        .navbar-nav .dropdown:hover .dropdown-menu {
            display: block;
            margin-top: 0;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 0;
            transition: all 0.3s ease;
        }

        .dropdown-item {
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border: none;
            color: var(--minimog-black);
            font-size: 0.9rem;
        }

        .dropdown-item:hover {
            background: var(--minimog-bg-light);
            color: var(--minimog-accent);
            transform: translateX(5px);
        }

        .dropdown-header {
            padding: 1rem 1.5rem 0.5rem;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid var(--minimog-bg-light);
            font-size: 0.8rem;
            letter-spacing: 0.5px;
        }

        /* Full-width dropdown for categories */
        .position-static {
            position: static !important;
        }

        .dropdown-menu.w-100 {
            left: 0 !important;
            right: 0 !important;
            transform: none !important;
            max-width: 100%;
        }

        /* Category dropdown specific styles */
        .navbar-nav .dropdown.position-static .dropdown-menu {
            width: 100%;
            margin-top: 0;
            border-radius: 0;
            border-top: 3px solid var(--minimog-accent);
        }

        /* Navigation link hover effects */
        .navbar-nav .nav-link {
            position: relative;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: var(--minimog-accent);
        }

        .navbar-nav .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 50%;
            background-color: var(--minimog-accent);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .navbar-nav .nav-link:hover::after {
            width: 100%;
        }
    </style>

    @yield('style')

    <!-- Global site tag (gtag.js) - Google Analytics -->
    @if (get_setting('google_analytics') == 1)
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ env('TRACKING_ID') }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', '{{ env('TRACKING_ID') }}');
        </script>
    @endif

    @if (get_setting('facebook_pixel') == 1)
        <!-- Facebook Pixel Code -->
        <script>
            ! function(f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function() {
                    n.callMethod ?
                        n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', {{ env('FACEBOOK_PIXEL_ID') }});
            fbq('track', 'PageView');
        </script>
        <noscript>
            <img height="1" width="1" style="display:none"
                src="https://www.facebook.com/tr?id={{ env('FACEBOOK_PIXEL_ID') }}&ev=PageView&noscript=1" />
        </noscript>
        <!-- End Facebook Pixel Code -->
    @endif
</head>

<body>
    <!-- Main Wrapper -->
    <div class="minimog-main-wrapper">
        @php
            $user = auth()->user();
            $user_avatar = null;
            $carts = [];
            if ($user && $user->avatar_original != null) {
                $user_avatar = uploaded_asset($user->avatar_original);
            }

            $system_language = get_system_language();
        @endphp

        <!-- Header -->
        @include('frontend.reclassic.partials.nav')

        <!-- Main Content -->
        <main class="minimog-content">
            @yield('content')
        </main>

        <!-- Footer -->
        @include('frontend.inc.footer')
    </div>

    @if(get_setting('show_cookies_agreement') == 'on')
        <div class="aiz-cookie-alert shadow-xl">
            <div class="p-3 bg-dark rounded">
                <div class="text-white mb-3">
                    {{strip_tags(get_setting('cookies_agreement_text')) }}
                </div>
                <button class="btn btn-primary aiz-cookie-accepet">
                    {{ translate('Ok. I Understood') }}
                </button>
            </div>
        </div>
    @endif

    @yield('modal')

    <!-- SCRIPTS -->
    <script src="{{ static_asset('assets/js/vendors.js') }}"></script>
    <script src="{{ static_asset('assets/js/aiz-core.js?v=') }}{{ rand(1000, 9999) }}"></script>

    @yield('script')

    <!-- Custom Scripts -->
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();
            
            // Initialize popovers
            $('[data-toggle="popover"]').popover();
            
            // Cookie consent
            if (localStorage.getItem('cookie-consent') === 'accepted') {
                $('.aiz-cookie-alert').hide();
            }

            $('.aiz-cookie-accepet').click(function() {
                localStorage.setItem('cookie-consent', 'accepted');
                $('.aiz-cookie-alert').fadeOut();
            });

            // Enhanced dropdown functionality
            $('.navbar-nav .dropdown').hover(
                function() {
                    $(this).find('.dropdown-menu').addClass('show');
                },
                function() {
                    $(this).find('.dropdown-menu').removeClass('show');
                }
            );

            // Mobile dropdown toggle
            $('.navbar-nav .dropdown-toggle').click(function(e) {
                if ($(window).width() < 992) {
                    e.preventDefault();
                    $(this).next('.dropdown-menu').toggleClass('show');
                }
            });
        });
    </script>
</body>
</html>
