# 🚀 Easy Hostinger Deployment Guide for Beginners

## What You Need Before Starting
- Hostinger account (any plan with PHP support)
- Your domain name
- This Laravel project folder on your computer

---

## Step 1: Get Your Hostinger Details 📝

### Login to <PERSON><PERSON> and collect these details:
1. **Domain**: Your website URL (e.g., `yoursite.com`)
2. **Database Info**: 
   - Go to `Databases` → `MySQL Databases`
   - Write down: Database name, Username, Password
3. **File Manager Access**: Available in your hPanel

---

## Step 2: Prepare Your Project Files 📁

### 2.1 Create a ZIP file of your project
1. Go to your project folder (`/Users/<USER>/Downloads/install`)
2. **Delete these folders** (they're not needed):
   - `node_modules/` 
   - `.git/` (if exists)
   - `tests/`
3. **Create a ZIP file** of the remaining project

### 2.2 Create your production environment file
Create a new file called `.env` with this content:
```
APP_NAME="TS Essential"
APP_ENV=production
APP_KEY=base64:WrvqXJ+ilWOLItqI7C2N2R2dQkqFAHFYzTrQBBJqtso=
APP_DEBUG=false
APP_URL="https://YOURDOMAINHERE.com"
APP_TIMEZONE="UTC"
SYSTEM_KEY="123456"

DEMO_MODE="Off"

DB_CONNECTION=mysql
DB_HOST="localhost"
DB_PORT="3306"
DB_DATABASE="YOUR_DATABASE_NAME_HERE"
DB_USERNAME="YOUR_DATABASE_USERNAME_HERE"
DB_PASSWORD="YOUR_DATABASE_PASSWORD_HERE"

CACHE_DRIVER="file"
SESSION_DRIVER="file"
QUEUE_CONNECTION=sync

MAIL_DRIVER="smtp"
MAIL_HOST="smtp.hostinger.com"
MAIL_PORT="465"
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="your_email_password"
MAIL_ENCRYPTION="ssl"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="TS Essential"
```

**Replace these with your actual details:**
- `YOURDOMAINHERE.com` → Your actual domain
- `YOUR_DATABASE_NAME_HERE` → From Step 1
- `YOUR_DATABASE_USERNAME_HERE` → From Step 1  
- `YOUR_DATABASE_PASSWORD_HERE` → From Step 1

---

## Step 3: Upload Database 🗄️

### 3.1 Export your local database
1. Open your local database tool (phpMyAdmin, etc.)
2. Export your database as `.sql` file
3. Save it as `database.sql`

### 3.2 Import to Hostinger
1. In Hostinger hPanel, go to `Databases` → `phpMyAdmin`
2. Click on your database name (left side)
3. Click `Import` tab
4. Choose your `database.sql` file
5. Click `Go`
6. Wait for "Import has been successfully finished"

---

## Step 4: Upload Your Website Files 📤

### 4.1 Upload via File Manager (Easiest Method)
1. In Hostinger hPanel, open `File Manager`
2. Go to `public_html` folder
3. **Delete everything** in `public_html` (it's usually just default files)
4. Upload your project ZIP file
5. Right-click the ZIP → `Extract`
6. Move all extracted files to `public_html` root (not in a subfolder)

### 4.2 Fix the file structure
Your `public_html` should look like this:
```
public_html/
├── app/
├── bootstrap/
├── config/
├── database/
├── public/
├── resources/
├── routes/
├── storage/
├── vendor/
├── .env
├── artisan
├── composer.json
└── index.php
```

### 4.3 Move public files (IMPORTANT!)
1. Go into the `public/` folder
2. **Select ALL files** in the `public/` folder
3. **Cut** them (Ctrl+X)
4. Go back to `public_html` root
5. **Paste** them (Ctrl+V)
6. **Delete** the now-empty `public/` folder

---

## Step 5: Create Important Files ⚙️

### 5.1 Update index.php
1. Open `index.php` in File Manager
2. Replace its content with:
```php
<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define('LARAVEL_START', microtime(true));

// Determine if the application is in maintenance mode...
if (file_exists($maintenance = __DIR__.'/storage/framework/maintenance.php')) {
    require $maintenance;
}

// Register the Composer autoloader...
require __DIR__.'/vendor/autoload.php';

// Bootstrap Laravel and handle the request...
(require_once __DIR__.'/bootstrap/app.php')
    ->handleRequest(Request::capture());
```

### 5.2 Create .htaccess file
1. In `public_html`, create new file called `.htaccess`
2. Add this content:
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle Laravel routes
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ index.php [QSA,L]
    
    # Hide sensitive files
    <Files .env>
        Order allow,deny
        Deny from all
    </Files>
</IfModule>

Options -Indexes
AddDefaultCharset UTF-8
```

### 5.3 Upload your .env file
1. Upload the `.env` file you created in Step 2.2
2. Place it in `public_html` root (same level as `index.php`)

---

## Step 6: Set Permissions 🔐

### Using File Manager:
1. Right-click on `storage` folder → `Permissions` → Set to `755`
2. Right-click on `bootstrap/cache` folder → `Permissions` → Set to `755`
3. Right-click on `.env` file → `Permissions` → Set to `644`

---

## Step 7: Test Your Website 🧪

1. Visit your domain: `https://yourdomain.com`
2. If you see your website → **SUCCESS!** 🎉
3. If you see errors → Go to Step 8

---

## Step 8: Fix Common Problems 🔧

### Problem: "500 Internal Server Error"
**Solution:**
1. Check if `.env` file exists and has correct database details
2. Check if `storage` folder has 755 permissions

### Problem: "Database connection error"
**Solution:**
1. Double-check database details in `.env` file
2. Make sure database was imported successfully

### Problem: "Page not found" or weird URLs
**Solution:**
1. Make sure `.htaccess` file exists in `public_html`
2. Check if all files from `public/` folder were moved correctly

### Problem: Images/CSS not loading
**Solution:**
1. Make sure you moved ALL files from `public/` folder to `public_html`
2. Check if `storage/app/public` folder exists

---

## Step 9: Enable SSL (HTTPS) 🔒

1. In Hostinger hPanel, go to `SSL`
2. Click `Enable` for Free SSL
3. Wait 10-15 minutes for activation
4. Your site will automatically use HTTPS

---

## Step 10: Final Checks ✅

Test these features on your live website:
- [ ] Homepage loads correctly
- [ ] Product pages work
- [ ] User can register/login
- [ ] Admin panel accessible
- [ ] Images display properly
- [ ] Search works

---

## 🆘 Need Help?

### If something goes wrong:
1. **Check error logs**: In hPanel → `Error Logs`
2. **Contact Hostinger support**: They have 24/7 chat support
3. **Double-check**: Database details, file permissions, .env file

### Emergency Reset:
If everything breaks:
1. Delete all files in `public_html`
2. Re-upload your ZIP file
3. Start from Step 4 again

---

## 🎉 Congratulations!

Your Laravel e-commerce website should now be live on Hostinger!

**Next Steps:**
- Change admin password
- Test all payment methods
- Set up email notifications
- Add your products
- Configure shipping settings

**Remember:**
- Keep your `.env` file secure
- Regular backups are important
- Update your application regularly
